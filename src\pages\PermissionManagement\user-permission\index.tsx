
import { SakuraTable } from '@/components/table';
import UserPermissionColums from './column';
import { useUserList} from '@/hooks/use-user';

import SakuraTableBar from '@/components/table/sakura-table-bar';

export default function UserPermissionManagementPage() {

    const {
        userListData,
    } = useUserList()



    const columns = UserPermissionColums()


    return (
        <div className="container mx-auto py-10 h-[calc(100vh-105px)]">
            <SakuraTableBar
                enableSearch={false}
                enableCreate={false}
                enableSelected={false}
                searchPlaceholder='搜索名称'
                createButtonText='创建用户'
            />
            <SakuraTable
                columns={columns}
                data={userListData?.users || []}
            />


        </div>
    )

}
